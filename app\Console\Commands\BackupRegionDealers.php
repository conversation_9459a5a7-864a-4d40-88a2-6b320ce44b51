<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\RegionDealerMigrationService;
use Exception;

class BackupRegionDealers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:region-dealers 
                            {--list : Mevcut yedekleri listele}
                            {--clean=5 : Eski yedekleri temizle (ka<PERSON> tane tutulacak)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Region Dealers verilerini yedekle ve yedek yönetimi yap';

    private RegionDealerMigrationService $migrationService;

    public function __construct(RegionDealerMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if ($this->option('list')) {
                $this->listBackups();
                return 0;
            }

            if ($this->option('clean')) {
                $keepCount = (int) $this->option('clean');
                $this->cleanBackups($keepCount);
                return 0;
            }

            // Normal yedekleme işlemi
            $this->createBackup();
            return 0;

        } catch (Exception $e) {
            $this->error('❌ Yedekleme hatası: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Yedek oluştur
     */
    private function createBackup()
    {
        $this->info('💾 Region Dealers verileri yedekleniyor...');
        
        $backupFile = $this->migrationService->backupCurrentRegionDealers();
        
        $this->info("✅ Yedek başarıyla oluşturuldu: {$backupFile}");
    }

    /**
     * Yedekleri listele
     */
    private function listBackups()
    {
        $this->info('📋 Mevcut Region Dealers Yedekleri:');
        
        $backups = $this->migrationService->listBackups();
        
        if (empty($backups)) {
            $this->warn('⚠️  Hiç yedek bulunamadı.');
            return;
        }

        $tableData = [];
        foreach ($backups as $backup) {
            $tableData[] = [
                $backup['name'],
                $this->formatFileSize($backup['size']),
                date('Y-m-d H:i:s', $backup['modified']),
            ];
        }

        $this->table(['Dosya Adı', 'Boyut', 'Tarih'], $tableData);
        
        $this->info("📊 Toplam {" . count($backups) . "} yedek dosyası bulundu.");
    }

    /**
     * Eski yedekleri temizle
     */
    private function cleanBackups(int $keepCount)
    {
        $this->info("🧹 Eski yedekler temizleniyor (son {$keepCount} yedek korunacak)...");
        
        $deletedCount = $this->migrationService->cleanOldBackups($keepCount);
        
        if ($deletedCount > 0) {
            $this->info("✅ {$deletedCount} eski yedek dosyası silindi.");
        } else {
            $this->info("ℹ️  Silinecek eski yedek bulunamadı.");
        }
    }

    /**
     * Dosya boyutunu formatla
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
