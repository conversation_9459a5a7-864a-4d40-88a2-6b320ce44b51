<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\RegionDealer;
use App\Models\Region;
use App\Models\Dealer;
use App\Services\RegionDealerMigrationService;
use Exception;

class MigrateRegionDealersFromMySQL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:region-dealers-from-mysql
                            {--dry-run : Sadece önizleme yap, veri kaydetme}
                            {--force : Mevcut verileri sil ve yeniden yükle}
                            {--chunk=100 : Kaç kayıt gruplarında işle}
                            {--backup : Migration öncesi yedek al}
                            {--validate : Migration sonrası doğrulama yap}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MySQL zone_branches tablosundan region_dealers verilerini PostgreSQL\'e taşır';

    private RegionDealerMigrationService $migrationService;

    public function __construct(RegionDealerMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 MySQL Zone Branches\'den PostgreSQL Region Dealers\'e Migration Başlatılıyor...');
        
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $chunkSize = (int) $this->option('chunk');
        $backup = $this->option('backup');
        $validate = $this->option('validate');

        try {
            // Migration istatistiklerini göster
            $this->showMigrationStats();

            // Region ve Dealer kontrolü
            if (!$this->checkPrerequisites()) {
                return 1;
            }

            // Yedek al
            if ($backup && !$dryRun) {
                $this->info('💾 Mevcut region_dealers verileri yedekleniyor...');
                $backupFile = $this->migrationService->backupCurrentRegionDealers();
                $this->info("✅ Yedek oluşturuldu: {$backupFile}");
            }

            // Mevcut verileri kontrol et
            if (!$force && !$dryRun && RegionDealer::count() > 0) {
                if (!$this->confirm('⚠️  Mevcut region_dealers verileri var. Devam etmek istiyor musunuz?')) {
                    $this->info('❌ Migration iptal edildi.');
                    return 0;
                }
            }

            // Migration işlemini başlat
            $this->performMigration($dryRun, $force, $chunkSize);

            // Doğrulama
            if ($validate && !$dryRun) {
                $this->info('🔍 Migration doğrulanıyor...');
                $this->validateMigration();
            }

            $this->info('✅ Migration başarıyla tamamlandı!');
            return 0;

        } catch (Exception $e) {
            $this->error('❌ Migration hatası: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Migration istatistiklerini göster
     */
    private function showMigrationStats()
    {
        try {
            $mysqlCount = DB::connection('mysql')->table('zone_branches')->count();
            $postgresCount = RegionDealer::count();

            $this->table(['Veritabanı', 'Tablo', 'Kayıt Sayısı'], [
                ['MySQL', 'zone_branches', $mysqlCount],
                ['PostgreSQL', 'region_dealers', $postgresCount],
            ]);

        } catch (Exception $e) {
            $this->warn('⚠️  İstatistik alınamadı: ' . $e->getMessage());
        }
    }

    /**
     * Ön koşulları kontrol et
     */
    private function checkPrerequisites(): bool
    {
        // Region kontrolü
        $regionCount = Region::count();
        if ($regionCount === 0) {
            $this->error('❌ PostgreSQL\'de region bulunamadı. Önce regions migration yapın.');
            return false;
        }

        // Dealer kontrolü
        $dealerCount = Dealer::count();
        if ($dealerCount === 0) {
            $this->error('❌ PostgreSQL\'de dealer bulunamadı. Önce dealers migration yapın.');
            return false;
        }

        $this->info("✅ {$regionCount} region ve {$dealerCount} dealer bulundu.");
        return true;
    }

    /**
     * Migration işlemini gerçekleştir
     */
    private function performMigration(bool $dryRun, bool $force, int $chunkSize)
    {
        // MySQL'den zone_branches verilerini çek
        $mysqlZoneBranches = $this->migrationService->fetchMySQLZoneBranches();
        
        if (empty($mysqlZoneBranches)) {
            $this->warn('⚠️  MySQL\'de zone_branches verisi bulunamadı.');
            return;
        }

        $this->info("📊 {" . count($mysqlZoneBranches) . "} zone_branches kaydı bulundu.");

        // Mevcut verileri sil (force mode)
        if ($force && !$dryRun) {
            $this->info('🗑️  Mevcut region_dealers verileri siliniyor...');
            RegionDealer::truncate();
        }

        // Progress bar
        $progressBar = $this->output->createProgressBar(count($mysqlZoneBranches));
        $progressBar->setFormat('verbose');

        $totalProcessed = 0;
        $totalErrors = 0;
        $chunks = array_chunk($mysqlZoneBranches, $chunkSize);

        foreach ($chunks as $chunk) {
            foreach ($chunk as $mysqlZoneBranch) {
                try {
                    $regionDealerData = $this->mapZoneBranchToRegionDealer($mysqlZoneBranch);

                    if (!$dryRun) {
                        RegionDealer::updateOrCreate(
                            [
                                'region_id' => $regionDealerData['region_id'],
                                'dealer_id' => $regionDealerData['dealer_id']
                            ],
                            $regionDealerData
                        );
                    }

                    $totalProcessed++;
                    $progressBar->advance();

                } catch (Exception $e) {
                    $totalErrors++;
                    $this->error("\n❌ Hata (ID: {$mysqlZoneBranch->id}): " . $e->getMessage());
                    $progressBar->advance();
                }
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 Dry Run tamamlandı. {" . count($mysqlZoneBranches) . "} kayıt işlenebilir durumda.");
        } else {
            $this->info("✅ {$totalProcessed} kayıt başarıyla işlendi.");
            if ($totalErrors > 0) {
                $this->warn("⚠️  {$totalErrors} kayıtta hata oluştu.");
            }
        }
    }

    /**
     * MySQL zone_branch verisini PostgreSQL region_dealer formatına dönüştür
     */
    private function mapZoneBranchToRegionDealer($mysqlZoneBranch)
    {
        // Zone ID'yi doğrudan Region ID olarak kullan
        $regionId = $mysqlZoneBranch->zone_id;

        // Branch ID'yi doğrudan Dealer ID olarak kullan
        $dealerId = $mysqlZoneBranch->branch_id;

        return [
            'region_id' => $regionId,
            'dealer_id' => $dealerId,
            'created_at' => $mysqlZoneBranch->created_at ?? now(),
            'updated_at' => $mysqlZoneBranch->updated_at ?? now(),
        ];
    }

    /**
     * Migration doğrulaması
     */
    private function validateMigration()
    {
        $mysqlCount = DB::connection('mysql')->table('zone_branches')->count();
        $postgresCount = RegionDealer::count();

        $this->table(['Kontrol', 'Sonuç'], [
            ['MySQL zone_branches sayısı', $mysqlCount],
            ['PostgreSQL region_dealers sayısı', $postgresCount],
            ['Durum', $mysqlCount === $postgresCount ? '✅ Eşleşiyor' : '⚠️  Farklı'],
        ]);

        if ($mysqlCount !== $postgresCount) {
            $this->warn('⚠️  Kayıt sayıları eşleşmiyor. Kontrol edin.');
        }
    }
}
