<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\RegionDealerMigrationService;
use Exception;

class RestoreRegionDealers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restore:region-dealers 
                            {backup? : Geri yüklenecek yedek dosyası}
                            {--latest : En son yedekten geri yükle}
                            {--list : Mevcut yedekleri listele}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Region Dealers verilerini yedekten geri yükle';

    private RegionDealerMigrationService $migrationService;

    public function __construct(RegionDealerMigrationService $migrationService)
    {
        parent::__construct();
        $this->migrationService = $migrationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if ($this->option('list')) {
                $this->listBackups();
                return 0;
            }

            $backupFile = $this->getBackupFile();
            
            if (!$backupFile) {
                $this->error('❌ Geri yüklenecek yedek dosyası bulunamadı.');
                return 1;
            }

            $this->restoreFromBackup($backupFile);
            return 0;

        } catch (Exception $e) {
            $this->error('❌ Geri yükleme hatası: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Geri yüklenecek yedek dosyasını belirle
     */
    private function getBackupFile(): ?string
    {
        // Komut satırından dosya belirtilmişse
        if ($this->argument('backup')) {
            $backupFile = 'migrations/region_dealers/' . $this->argument('backup');
            return $backupFile;
        }

        // En son yedek seçeneği
        if ($this->option('latest')) {
            $backups = $this->migrationService->listBackups();
            if (empty($backups)) {
                $this->error('❌ Hiç yedek bulunamadı.');
                return null;
            }
            return $backups[0]['file']; // En yeni yedek
        }

        // İnteraktif seçim
        return $this->selectBackupInteractively();
    }

    /**
     * İnteraktif yedek seçimi
     */
    private function selectBackupInteractively(): ?string
    {
        $backups = $this->migrationService->listBackups();
        
        if (empty($backups)) {
            $this->error('❌ Hiç yedek bulunamadı.');
            return null;
        }

        $this->info('📋 Mevcut Region Dealers Yedekleri:');
        
        $choices = [];
        foreach ($backups as $index => $backup) {
            $choices[] = sprintf(
                '%d. %s (%s - %s)',
                $index + 1,
                $backup['name'],
                $this->formatFileSize($backup['size']),
                date('Y-m-d H:i:s', $backup['modified'])
            );
        }

        $choice = $this->choice(
            'Hangi yedekten geri yüklemek istiyorsunuz?',
            $choices,
            0
        );

        // Seçilen index'i bul
        $selectedIndex = (int) substr($choice, 0, strpos($choice, '.')) - 1;
        
        return $backups[$selectedIndex]['file'];
    }

    /**
     * Yedekten geri yükle
     */
    private function restoreFromBackup(string $backupFile)
    {
        $this->info("🔄 Yedekten geri yükleniyor: {$backupFile}");
        
        // Onay al
        if (!$this->confirm('⚠️  Bu işlem mevcut tüm region_dealers verilerini silecek. Devam etmek istiyor musunuz?')) {
            $this->info('❌ Geri yükleme iptal edildi.');
            return;
        }

        // Geri yükleme işlemini gerçekleştir
        $success = $this->migrationService->restoreFromBackup($backupFile);
        
        if ($success) {
            $this->info('✅ Region Dealers verileri başarıyla geri yüklendi!');
            
            // İstatistikleri göster
            $this->showRestoreStats();
        } else {
            $this->error('❌ Geri yükleme başarısız oldu.');
        }
    }

    /**
     * Yedekleri listele
     */
    private function listBackups()
    {
        $this->info('📋 Mevcut Region Dealers Yedekleri:');
        
        $backups = $this->migrationService->listBackups();
        
        if (empty($backups)) {
            $this->warn('⚠️  Hiç yedek bulunamadı.');
            return;
        }

        $tableData = [];
        foreach ($backups as $backup) {
            $tableData[] = [
                $backup['name'],
                $this->formatFileSize($backup['size']),
                date('Y-m-d H:i:s', $backup['modified']),
            ];
        }

        $this->table(['Dosya Adı', 'Boyut', 'Tarih'], $tableData);
        
        $this->info("📊 Toplam {" . count($backups) . "} yedek dosyası bulundu.");
    }

    /**
     * Geri yükleme istatistiklerini göster
     */
    private function showRestoreStats()
    {
        $regionDealerCount = \App\Models\RegionDealer::count();
        
        $this->table(['Tablo', 'Kayıt Sayısı'], [
            ['region_dealers', $regionDealerCount],
        ]);
    }

    /**
     * Dosya boyutunu formatla
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
