<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Region;
use App\Models\Dealer;
use App\Models\RegionDealer;

class RegionDealerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get regions and dealers
        $marmara = Region::where('name', 'Marmara Bölgesi')->first();
        $ege = Region::where('name', 'Ege Bölgesi')->first();
        $akdeniz = Region::where('name', 'Akdeniz Bölgesi')->first();
        $icAnadolu = Region::where('name', 'İç Anadolu Bölgesi')->first();
        $karadeniz = Region::where('name', 'Karadeniz Bölgesi')->first();

        $istanbulDealer = Dealer::where('name', 'İstanbul Merkez Bayi')->first();
        $bursaDealer = Dealer::where('name', 'Bursa Bayi')->first();
        $izmirDealer = Dealer::where('name', 'İzmir Bayi')->first();
        $antalyaDealer = Dealer::where('name', 'Antalya Bayi')->first();
        $ankaraDealer = Dealer::where('name', 'Ankara Bayi')->first();

        // Create region-dealer relationships
        $regionDealers = [
            // İstanbul Merkez Bayi multiple regions
            ['region_id' => $marmara->id, 'dealer_id' => $istanbulDealer->id],
            ['region_id' => $ege->id, 'dealer_id' => $istanbulDealer->id],

            // Bursa Bayi
            ['region_id' => $marmara->id, 'dealer_id' => $bursaDealer->id],

            // İzmir Bayi
            ['region_id' => $ege->id, 'dealer_id' => $izmirDealer->id],
            ['region_id' => $akdeniz->id, 'dealer_id' => $izmirDealer->id],

            // Antalya Bayi
            ['region_id' => $akdeniz->id, 'dealer_id' => $antalyaDealer->id],

            // Ankara Bayi
            ['region_id' => $icAnadolu->id, 'dealer_id' => $ankaraDealer->id],
            ['region_id' => $karadeniz->id, 'dealer_id' => $ankaraDealer->id],
        ];

        foreach ($regionDealers as $regionDealer) {
            RegionDealer::updateOrCreate(
                [
                    'region_id' => $regionDealer['region_id'],
                    'dealer_id' => $regionDealer['dealer_id']
                ],
                $regionDealer
            );
        }
    }
}
